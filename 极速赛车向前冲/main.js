const axios = require('axios');
const { HttpsProxyAgent } = require('https-proxy-agent');

process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const CONFIG = {
    proxy: { host: '127.0.0.1', port: 2024 },
    headers: {
        'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'Keep-Alive', 'Content-Type': 'application/json; Charset=UTF-8',
        'Host': 'sdk.mini.stargame.group', 'Referer': 'https://servicewechat.com/wx3f2ac2c06e796c9b/78/page-frame.html',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540517) XWEB/13909',
        'xweb_xhr': '1'
    }
};

async function makeRequest({ method = 'GET', url, data, authorization, headers = {}, timeout = 30000 }) {
    try {
        const { proxy } = CONFIG;
        const proxyUrl = proxy.username ?
            `http://${proxy.username}:${proxy.password}@${proxy.host}:${proxy.port}` :
            `http://${proxy.host}:${proxy.port}`;

        const config = {
            method: method.toUpperCase(), url, timeout,
            headers: { ...CONFIG.headers, ...headers, ...(authorization && { Authorization: authorization }) },
            httpsAgent: new HttpsProxyAgent(proxyUrl, { rejectUnauthorized: false }),
            ...(data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) && { data })
        };

        console.log(`${method.toUpperCase()} ${url}`);
        const response = await axios(config);
        console.log(`✓ ${response.status}`, response.data);
        return { success: true, status: response.status, headers: response.headers, data: response.data };

    } catch (error) {
        console.error(`✗ ${error.message}`);
        return { success: false, message: error.message, status: error.response?.status, data: error.response?.data };
    }
}

// 发送游戏存档请求
async function getGameArchive() {
    const authorization = 'Bearer eyJhbGciOiJIUzI1NiJ9.eyJnYW1lSWQiOjEwLCJzaWduSW5UeXBlIjoiVVNFUiIsImdhbWVDaGFubmVsTWFzdGVyQ29kZU5vIjoiV0VDSEFUIiwic2Vzc2lvbktleSI6IlBBbTVwOW14Uzg0Y0N6VGRuVlBOVEE9PSIsImdhbWVDaGFubmVsTWFzdGVySWQiOjE2MSwiZ2FtZVVzZXJJZCI6MTk1NzY5MTE4NDY4NDA3NzA1NywiZ2FtZVVzZXJFeHRlcm5hbElkIjoibzZwaVg2Njh2eEVFQW1PRjZPM2FuNEszQ052ZyIsImdhbWVDaGFubmVsSWQiOjE3fQ.vUbIRP1bOWbzUtQgkYGhlB-fsTb9bWNvWgxhMkDY8cM';

    return await makeRequest({
        method: 'GET',
        url: 'https://sdk.mini.stargame.group/api/game_user/get_game_archive',
        authorization: authorization
    });
}

// 主函数
async function main() {
    try {
        console.log('开始执行游戏存档请求...');
        const result = await getGameArchive();
        console.log('执行完成！');
    } catch (error) {
        console.error('执行失败:', error.message);
        process.exit(1);
    }
}

// 执行主函数
main();
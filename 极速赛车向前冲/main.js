const axios = require('axios');
const { HttpsProxyAgent } = require('https-proxy-agent');
const https = require('https');

// 全局禁用SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

// 代理配置 - 请根据实际情况修改
const PROXY_CONFIG = {
    host: '127.0.0.1',  // 代理服务器地址
    port: 2024,         // 代理服务器端口
    // username: 'your_username',  // 如果需要认证，取消注释并填写
    // password: 'your_password'   // 如果需要认证，取消注释并填写
};

// 创建代理agent
function createProxyAgent() {
    const proxyUrl = PROXY_CONFIG.username && PROXY_CONFIG.password
        ? `http://${PROXY_CONFIG.username}:${PROXY_CONFIG.password}@${PROXY_CONFIG.host}:${PROXY_CONFIG.port}`
        : `http://${PROXY_CONFIG.host}:${PROXY_CONFIG.port}`;

    return new HttpsProxyAgent(proxyUrl, {
        rejectUnauthorized: false,
        secureProtocol: 'TLSv1_2_method'
    });
}

// 发送游戏存档请求
async function getGameArchive() {
    try {
        const proxyAgent = createProxyAgent();

        const config = {
            method: 'GET',
            url: 'https://sdk.mini.stargame.group/api/game_user/get_game_archive',
            headers: {
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiJ9.eyJnYW1lSWQiOjEwLCJzaWduSW5UeXBlIjoiVVNFUiIsImdhbWVDaGFubmVsTWFzdGVyQ29kZU5vIjoiV0VDSEFUIiwic2Vzc2lvbktleSI6IlBBbTVwOW14Uzg0Y0N6VGRuVlBOVEE9PSIsImdhbWVDaGFubmVsTWFzdGVySWQiOjE2MSwiZ2FtZVVzZXJJZCI6MTk1NzY5MTE4NDY4NDA3NzA1NywiZ2FtZVVzZXJFeHRlcm5hbElkIjoibzZwaVg2Njh2eEVFQW1PRjZPM2FuNEszQ052ZyIsImdhbWVDaGFubmVsSWQiOjE3fQ.vUbIRP1bOWbzUtQgkYGhlB-fsTb9bWNvWgxhMkDY8cM',
                'Connection': 'Keep-Alive',
                'Content-Type': 'application/json; Charset=UTF-8',
                'Host': 'sdk.mini.stargame.group',
                'Referer': 'https://servicewechat.com/wx3f2ac2c06e796c9b/78/page-frame.html',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540517) XWEB/13909',
                'xweb_xhr': '1'
            },
            httpsAgent: proxyAgent,
            timeout: 30000, // 30秒超时
            // 忽略SSL证书验证错误
            rejectUnauthorized: false
        };

        console.log('正在通过代理发送请求...');
        console.log(`代理地址: ${PROXY_CONFIG.host}:${PROXY_CONFIG.port}`);

        const response = await axios(config);

        console.log('请求成功！');
        console.log('状态码:', response.status);
        console.log('响应头:', JSON.stringify(response.headers, null, 2));
        console.log('响应数据:', JSON.stringify(response.data, null, 2));

        return response.data;

    } catch (error) {
        console.error('请求失败:', error.message);

        if (error.response) {
            console.error('响应状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        } else if (error.request) {
            console.error('请求未收到响应');
        }

        throw error;
    }
}

// 主函数
async function main() {
    try {
        console.log('开始执行游戏存档请求...');
        const result = await getGameArchive();
        console.log('执行完成！');
    } catch (error) {
        console.error('执行失败:', error.message);
        process.exit(1);
    }
}

// 执行主函数
main();
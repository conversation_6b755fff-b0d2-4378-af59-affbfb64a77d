<?php
/**
 * 全局配置
 */
$baseUrl = "https://sdk.mini.stargame.group/api";
$token   = "eyJhbGciOiJIUzI1NiJ9.eyJnYW1lSWQiOjEwLCJzaWduSW5UeXBlIjoiVVNFUiIsImdhbWVDaGFubmVsTWFzdGVyQ29kZU5vIjoiV0VDSEFUIiwic2Vzc2lvbktleSI6IlBBbTVwOW14Uzg0Y0N6VGRuVlBOVEE9PSIsImdhbWVDaGFubmVsTWFzdGVySWQiOjE2MSwiZ2FtZVVzZXJJZCI6MTk1NzY5MTE4NDY4NDA3NzA1NywiZ2FtZVVzZXJFeHRlcm5hbElkIjoibzZwaVg2Njh2eEVFQW1PRjZPM2FuNEszQ052ZyIsImdhbWVDaGFubmVsSWQiOjE3fQ.vUbIRP1bOWbzUtQgkYGhlB-fsTb9bWNvWgxhMkDY8cM";  // ⚠️注意替换

$headers = [
    "Authorization: Bearer " . $token,
    "Content-Type: application/json",
    "Accept: */*",
    "xweb_xhr: 1",
    "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540517) XWEB/13909"
];

/**
 * 通用 cURL 方法
 */
function sendRequest($url, $headers, $payload = null) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_PROXY, "127.0.0.1:2024");

    if (!is_null($payload)) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload, JSON_UNESCAPED_UNICODE));
    }

    $response = curl_exec($ch);
    if ($response === false) {
        die("cURL Error: " . curl_error($ch));
    }
    curl_close($ch);

    return $response;
}

/**
 * 获取存档
 */
function getArchive($baseUrl, $headers) {
    $url = $baseUrl . "/game_user/get_game_archive"; // ⚠️确认路径
    $response = sendRequest($url, $headers);
    $res = json_decode($response, true);
    if (!$res || !$res['succeed']) {
        die("获取存档失败: " . $response);
    }

    $archive_hex = str_replace("\n", "", $res['data']['archive']);
    $binary_data = hex2bin($archive_hex);
    if ($binary_data === false) {
        die("hex2bin 转换失败");
    }

    $decoded = gzdecode($binary_data);
    if ($decoded === false) {
        die("gzdecode 解压失败");
    }

    $data = json_decode($decoded, true);
    if ($data === null) {
        die("json_decode 失败，原始内容: " . $decoded);
    }
    $data = json_decode($data, true);
    echo "当前存档内容:\n";
    print_r($data);

    return [
        'data' => $data,
        'version' => intval($res['data']['version'])
    ];
}

/**
 * 修改并上传存档
 */
function updateArchive($baseUrl, $headers, $data, $version) {
    $url = $baseUrl . "/game_user/sync_game_archive";

    // 示例：改金币为 999999
    $data['5'] = 999999;


    // 压缩 + 转 HEX
    $jsonStr = json_encode($data, JSON_UNESCAPED_UNICODE);
    $gzData  = gzencode($jsonStr);
    $archiveHex = bin2hex($gzData);

    $payload = [
        "version" => $version , // 一般需要递增
        "archive" => $archiveHex
    ];

    $response = sendRequest($url, $headers, $payload);
    echo "上传结果:\n";
    echo $response;
}

/**
 * 主逻辑
 */
$archiveInfo = getArchive($baseUrl, $headers);
// 调试时可以先注释上传

//updateArchive($baseUrl, $headers, $archiveInfo['data'], $archiveInfo['version']);
